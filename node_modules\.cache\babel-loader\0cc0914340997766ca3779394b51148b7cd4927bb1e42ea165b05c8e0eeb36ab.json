{"ast": null, "code": "var _jsxFileName = \"D:\\\\LG\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  var _user$name, _user$name2;\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n  const dropdownRef = useRef(null);\n  const location = useLocation();\n  const getPageTitle = () => {\n    switch (location.pathname) {\n      case '/dashboard':\n        return 'Dashboard';\n      case '/transactions':\n        return 'All Transactions';\n      default:\n        return 'Dashboard';\n    }\n  };\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsDropdownOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const handleLogout = () => {\n    logout();\n    setIsDropdownOpen(false);\n  };\n  const toggleDropdown = () => {\n    setIsDropdownOpen(!isDropdownOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: getPageTitle()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-dropdown\",\n          ref: dropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-button\",\n            onClick: toggleDropdown,\n            \"aria-expanded\": isDropdownOpen,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0)) || 'A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"profile-name\",\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'Admin'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"profile-role\",\n                children: \"Administrator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `dropdown-arrow ${isDropdownOpen ? 'open' : ''}`,\n              children: \"\\u25BC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), isDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dropdown-user-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dropdown-avatar\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (user === null || user === void 0 ? void 0 : (_user$name2 = user.name) === null || _user$name2 === void 0 ? void 0 : _user$name2.charAt(0)) || 'A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 76,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dropdown-name\",\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'Admin'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dropdown-username\",\n                    children: [\"@\", (user === null || user === void 0 ? void 0 : user.username) || 'admin']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-divider\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-items\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dropdown-icon\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this), \"Profile Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dropdown-icon\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this), \"Account Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dropdown-icon\",\n                  children: \"\\u2753\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this), \"Help & Support\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-divider\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-items\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"dropdown-item logout-item\",\n                onClick: handleLogout,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dropdown-icon\",\n                  children: \"\\uD83D\\uDEAA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"Gr+SEjw1l82fyevZUMlnKfUoavM=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Header", "_s", "_user$name", "_user$name2", "isDropdownOpen", "setIsDropdownOpen", "user", "logout", "dropdownRef", "location", "getPageTitle", "pathname", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleLogout", "toggleDropdown", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "name", "char<PERSON>t", "username", "_c", "$RefreshReg$"], "sources": ["D:/LG/src/components/Layout/Header.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport './Header.css';\n\nconst Header: React.FC = () => {\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const location = useLocation();\n\n  const getPageTitle = () => {\n    switch (location.pathname) {\n      case '/dashboard':\n        return 'Dashboard';\n      case '/transactions':\n        return 'All Transactions';\n      default:\n        return 'Dashboard';\n    }\n  };\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = () => {\n    logout();\n    setIsDropdownOpen(false);\n  };\n\n  const toggleDropdown = () => {\n    setIsDropdownOpen(!isDropdownOpen);\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-content\">\n        <div className=\"header-left\">\n          <h1 className=\"page-title\">{getPageTitle()}</h1>\n        </div>\n\n        <div className=\"header-right\">\n          <div className=\"profile-dropdown\" ref={dropdownRef}>\n            <button\n              className=\"profile-button\"\n              onClick={toggleDropdown}\n              aria-expanded={isDropdownOpen}\n            >\n              <div className=\"profile-avatar\">\n                <span>{user?.name?.charAt(0) || 'A'}</span>\n              </div>\n              <div className=\"profile-info\">\n                <span className=\"profile-name\">{user?.name || 'Admin'}</span>\n                <span className=\"profile-role\">Administrator</span>\n              </div>\n              <span className={`dropdown-arrow ${isDropdownOpen ? 'open' : ''}`}>\n                ▼\n              </span>\n            </button>\n\n            {isDropdownOpen && (\n              <div className=\"dropdown-menu\">\n                <div className=\"dropdown-header\">\n                  <div className=\"dropdown-user-info\">\n                    <div className=\"dropdown-avatar\">\n                      <span>{user?.name?.charAt(0) || 'A'}</span>\n                    </div>\n                    <div>\n                      <div className=\"dropdown-name\">{user?.name || 'Admin'}</div>\n                      <div className=\"dropdown-username\">@{user?.username || 'admin'}</div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"dropdown-divider\"></div>\n                \n                <div className=\"dropdown-items\">\n                  <button className=\"dropdown-item\">\n                    <span className=\"dropdown-icon\">👤</span>\n                    Profile Settings\n                  </button>\n                  <button className=\"dropdown-item\">\n                    <span className=\"dropdown-icon\">⚙️</span>\n                    Account Settings\n                  </button>\n                  <button className=\"dropdown-item\">\n                    <span className=\"dropdown-icon\">❓</span>\n                    Help & Support\n                  </button>\n                </div>\n\n                <div className=\"dropdown-divider\"></div>\n\n                <div className=\"dropdown-items\">\n                  <button className=\"dropdown-item logout-item\" onClick={handleLogout}>\n                    <span className=\"dropdown-icon\">🚪</span>\n                    Logout\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA;EAC7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEa,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAMW,WAAW,GAAGd,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMe,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQD,QAAQ,CAACE,QAAQ;MACvB,KAAK,YAAY;QACf,OAAO,WAAW;MACpB,KAAK,eAAe;QAClB,OAAO,kBAAkB;MAC3B;QACE,OAAO,WAAW;IACtB;EACF,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACd,MAAMiB,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIL,WAAW,CAACM,OAAO,IAAI,CAACN,WAAW,CAACM,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC9EX,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDY,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBb,MAAM,CAAC,CAAC;IACRF,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3BhB,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,oBACEL,OAAA;IAAQuB,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBxB,OAAA;MAAKuB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxB,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxB,OAAA;UAAIuB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEb,YAAY,CAAC;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxB,OAAA;UAAKuB,SAAS,EAAC,kBAAkB;UAACM,GAAG,EAAEpB,WAAY;UAAAe,QAAA,gBACjDxB,OAAA;YACEuB,SAAS,EAAC,gBAAgB;YAC1BO,OAAO,EAAER,cAAe;YACxB,iBAAejB,cAAe;YAAAmB,QAAA,gBAE9BxB,OAAA;cAAKuB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BxB,OAAA;gBAAAwB,QAAA,EAAO,CAAAjB,IAAI,aAAJA,IAAI,wBAAAJ,UAAA,GAAJI,IAAI,CAAEwB,IAAI,cAAA5B,UAAA,uBAAVA,UAAA,CAAY6B,MAAM,CAAC,CAAC,CAAC,KAAI;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN5B,OAAA;cAAKuB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxB,OAAA;gBAAMuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7D5B,OAAA;gBAAMuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACN5B,OAAA;cAAMuB,SAAS,EAAE,kBAAkBlB,cAAc,GAAG,MAAM,GAAG,EAAE,EAAG;cAAAmB,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAERvB,cAAc,iBACbL,OAAA;YAAKuB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxB,OAAA;cAAKuB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BxB,OAAA;gBAAKuB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCxB,OAAA;kBAAKuB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BxB,OAAA;oBAAAwB,QAAA,EAAO,CAAAjB,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAEwB,IAAI,cAAA3B,WAAA,uBAAVA,WAAA,CAAY4B,MAAM,CAAC,CAAC,CAAC,KAAI;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN5B,OAAA;kBAAAwB,QAAA,gBACExB,OAAA;oBAAKuB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI;kBAAO;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5D5B,OAAA;oBAAKuB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,GAAC,GAAC,EAAC,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,QAAQ,KAAI,OAAO;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5B,OAAA;cAAKuB,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAExC5B,OAAA;cAAKuB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BxB,OAAA;gBAAQuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC/BxB,OAAA;kBAAMuB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oBAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5B,OAAA;gBAAQuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC/BxB,OAAA;kBAAMuB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oBAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5B,OAAA;gBAAQuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC/BxB,OAAA;kBAAMuB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,kBAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5B,OAAA;cAAKuB,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAExC5B,OAAA;cAAKuB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BxB,OAAA;gBAAQuB,SAAS,EAAC,2BAA2B;gBAACO,OAAO,EAAET,YAAa;gBAAAG,QAAA,gBAClExB,OAAA;kBAAMuB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,UAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC1B,EAAA,CA/GID,MAAgB;EAAA,QAEKH,OAAO,EAEfD,WAAW;AAAA;AAAAqC,EAAA,GAJxBjC,MAAgB;AAiHtB,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}