{"ast": null, "code": "// API service layer for all HTTP requests\nconst BASE_API_URL = process.env.REACT_APP_BASE_API || 'https://jsonplaceholder.typicode.com';\nclass ApiService {\n  constructor() {\n    this.baseUrl = void 0;\n    this.baseUrl = BASE_API_URL;\n  }\n\n  // Generic fetch wrapper with error handling\n  async fetchWithErrorHandling(endpoint, options = {}) {\n    try {\n      const response = await fetch(`${this.baseUrl}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        ...options\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Authentication API (simulated since we're using hardcoded credentials)\n  async login(credentials) {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Hardcoded credentials validation\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      return {\n        success: true,\n        message: 'Login successful',\n        user: {\n          id: 1,\n          username: 'admin',\n          name: 'Administrator'\n        }\n      };\n    } else {\n      return {\n        success: false,\n        message: 'Invalid username or password'\n      };\n    }\n  }\n\n  // Get all users for transactions table\n  async getUsers() {\n    return this.fetchWithErrorHandling('/users');\n  }\n\n  // Get dashboard statistics (simulated data)\n  async getDashboardStats() {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    // Return mock data for dashboard widgets\n    return [{\n      platform: 'Facebook',\n      count: 23\n    }, {\n      platform: 'Google',\n      count: 55\n    }, {\n      platform: 'Twitter',\n      count: 12\n    }, {\n      platform: 'LinkedIn',\n      count: 8\n    }, {\n      platform: 'Instagram',\n      count: 34\n    }, {\n      platform: 'YouTube',\n      count: 19\n    }];\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["BASE_API_URL", "process", "env", "REACT_APP_BASE_API", "ApiService", "constructor", "baseUrl", "fetchWithErrorHandling", "endpoint", "options", "response", "fetch", "headers", "ok", "Error", "status", "json", "error", "console", "login", "credentials", "Promise", "resolve", "setTimeout", "username", "password", "success", "message", "user", "id", "name", "getUsers", "getDashboardStats", "platform", "count", "apiService"], "sources": ["D:/LG/src/api.tsx"], "sourcesContent": ["// API service layer for all HTTP requests\nconst BASE_API_URL = process.env.REACT_APP_BASE_API || 'https://jsonplaceholder.typicode.com';\n\nexport interface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  address: {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n    geo: {\n      lat: string;\n      lng: string;\n    };\n  };\n  phone: string;\n  website: string;\n  company: {\n    name: string;\n    catchPhrase: string;\n    bs: string;\n  };\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  user?: {\n    id: number;\n    username: string;\n    name: string;\n  };\n}\n\nclass ApiService {\n  private baseUrl: string;\n\n  constructor() {\n    this.baseUrl = BASE_API_URL;\n  }\n\n  // Generic fetch wrapper with error handling\n  private async fetchWithErrorHandling<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    try {\n      const response = await fetch(`${this.baseUrl}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Authentication API (simulated since we're using hardcoded credentials)\n  async login(credentials: LoginCredentials): Promise<LoginResponse> {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Hardcoded credentials validation\n    if (credentials.username === 'admin' && credentials.password === 'admin@123') {\n      return {\n        success: true,\n        message: 'Login successful',\n        user: {\n          id: 1,\n          username: 'admin',\n          name: 'Administrator'\n        }\n      };\n    } else {\n      return {\n        success: false,\n        message: 'Invalid username or password'\n      };\n    }\n  }\n\n  // Get all users for transactions table\n  async getUsers(): Promise<User[]> {\n    return this.fetchWithErrorHandling<User[]>('/users');\n  }\n\n  // Get dashboard statistics (simulated data)\n  async getDashboardStats(): Promise<{ platform: string; count: number }[]> {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    // Return mock data for dashboard widgets\n    return [\n      { platform: 'Facebook', count: 23 },\n      { platform: 'Google', count: 55 },\n      { platform: 'Twitter', count: 12 },\n      { platform: 'LinkedIn', count: 8 },\n      { platform: 'Instagram', count: 34 },\n      { platform: 'YouTube', count: 19 }\n    ];\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,IAAI,sCAAsC;AAyC7F,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,OAAO;IAGb,IAAI,CAACA,OAAO,GAAGN,YAAY;EAC7B;;EAEA;EACA,MAAcO,sBAAsBA,CAClCC,QAAgB,EAChBC,OAAoB,GAAG,CAAC,CAAC,EACb;IACZ,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,GAAGE,QAAQ,EAAE,EAAE;QACzDI,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,GAAGH,OAAO,CAACG;QACb,CAAC;QACD,GAAGH;MACL,CAAC,CAAC;MAEF,IAAI,CAACC,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBJ,QAAQ,CAACK,MAAM,EAAE,CAAC;MAC3D;MAEA,OAAO,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAME,KAAKA,CAACC,WAA6B,EAA0B;IACjE;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,IAAIF,WAAW,CAACI,QAAQ,KAAK,OAAO,IAAIJ,WAAW,CAACK,QAAQ,KAAK,WAAW,EAAE;MAC5E,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,kBAAkB;QAC3BC,IAAI,EAAE;UACJC,EAAE,EAAE,CAAC;UACLL,QAAQ,EAAE,OAAO;UACjBM,IAAI,EAAE;QACR;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF;;EAEA;EACA,MAAMI,QAAQA,CAAA,EAAoB;IAChC,OAAO,IAAI,CAACxB,sBAAsB,CAAS,QAAQ,CAAC;EACtD;;EAEA;EACA,MAAMyB,iBAAiBA,CAAA,EAAmD;IACxE;IACA,MAAM,IAAIX,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,OAAO,CACL;MAAEW,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAC,EACnC;MAAED,QAAQ,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAG,CAAC,EACjC;MAAED,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAG,CAAC,EAClC;MAAED,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAE,CAAC,EAClC;MAAED,QAAQ,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAG,CAAC,EACpC;MAAED,QAAQ,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAG,CAAC,CACnC;EACH;AACF;;AAEA;AACA,OAAO,MAAMC,UAAU,GAAG,IAAI/B,UAAU,CAAC,CAAC;AAC1C,eAAe+B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}