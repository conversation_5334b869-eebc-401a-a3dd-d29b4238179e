{"ast": null, "code": "var _jsxFileName = \"D:\\\\LG\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is already logged in on app start\n    const storedUser = localStorage.getItem('user');\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        localStorage.removeItem('user');\n      }\n    }\n    setIsLoading(false);\n  }, []);\n  const login = userData => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('user');\n  };\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    login,\n    logout,\n    isLoading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "isLoading", "setIsLoading", "storedUser", "localStorage", "getItem", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "userData", "setItem", "stringify", "logout", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/LG/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ninterface User {\n  id: number;\n  username: string;\n  name: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  login: (user: User) => void;\n  logout: () => void;\n  isLoading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is already logged in on app start\n    const storedUser = localStorage.getItem('user');\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        localStorage.removeItem('user');\n      }\n    }\n    setIsLoading(false);\n  }, []);\n\n  const login = (userData: User) => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('user');\n  };\n\n  const value: AuthContextType = {\n    user,\n    isAuthenticated: !!user,\n    login,\n    logout,\n    isLoading,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBzF,MAAMC,WAAW,gBAAGN,aAAa,CAA8BO,SAAS,CAAC;AAMzE,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMY,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,IAAIF,UAAU,EAAE;MACd,IAAI;QACF,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACzCH,OAAO,CAACM,UAAU,CAAC;MACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDL,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAT,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,KAAK,GAAIC,QAAc,IAAK;IAChCb,OAAO,CAACa,QAAQ,CAAC;IACjBT,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACF,QAAQ,CAAC,CAAC;EACxD,CAAC;EAED,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnBhB,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED,MAAMM,KAAsB,GAAG;IAC7BlB,IAAI;IACJmB,eAAe,EAAE,CAAC,CAACnB,IAAI;IACvBa,KAAK;IACLI,MAAM;IACNf;EACF,CAAC;EAED,oBACER,OAAA,CAACC,WAAW,CAACyB,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAApB,QAAA,EAChCA;EAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzB,EAAA,CA1CWF,YAAyC;AAAA4B,EAAA,GAAzC5B,YAAyC;AA4CtD,OAAO,MAAM6B,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGtC,UAAU,CAACK,WAAW,CAAC;EACvC,IAAIiC,OAAO,KAAKhC,SAAS,EAAE;IACzB,MAAM,IAAIiC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}