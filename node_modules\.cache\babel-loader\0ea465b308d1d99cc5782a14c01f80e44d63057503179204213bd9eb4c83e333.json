{"ast": null, "code": "var _jsxFileName = \"D:\\\\LG\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\";\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  const menuItems = [{\n    path: '/dashboard',\n    label: 'Dashboard',\n    icon: '📊'\n  }, {\n    path: '/transactions',\n    label: 'All Transactions',\n    icon: '📋'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-icon\",\n          children: \"\\u26A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"sidebar-nav\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"nav-list\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"nav-item\",\n          children: /*#__PURE__*/_jsxDEV(NavLink, {\n            to: item.path,\n            className: ({\n              isActive\n            }) => `nav-link ${isActive ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-label\",\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)\n        }, item.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-version\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"v1.0.0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "jsxDEV", "_jsxDEV", "Sidebar", "menuItems", "path", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "isActive", "_c", "$RefreshReg$"], "sources": ["D:/LG/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport './Sidebar.css';\n\nconst Sidebar: React.FC = () => {\n  const menuItems = [\n    {\n      path: '/dashboard',\n      label: 'Dashboard',\n      icon: '📊'\n    },\n    {\n      path: '/transactions',\n      label: 'All Transactions',\n      icon: '📋'\n    }\n  ];\n\n  return (\n    <div className=\"sidebar\">\n      <div className=\"sidebar-header\">\n        <div className=\"sidebar-logo\">\n          <span className=\"logo-icon\">⚡</span>\n          <span className=\"logo-text\">Admin</span>\n        </div>\n      </div>\n\n      <nav className=\"sidebar-nav\">\n        <ul className=\"nav-list\">\n          {menuItems.map((item) => (\n            <li key={item.path} className=\"nav-item\">\n              <NavLink\n                to={item.path}\n                className={({ isActive }) =>\n                  `nav-link ${isActive ? 'active' : ''}`\n                }\n              >\n                <span className=\"nav-icon\">{item.icon}</span>\n                <span className=\"nav-label\">{item.label}</span>\n              </NavLink>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      <div className=\"sidebar-footer\">\n        <div className=\"sidebar-version\">\n          <span>v1.0.0</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEL,OAAA;IAAKM,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBP,OAAA;MAAKM,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BP,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BP,OAAA;UAAMM,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCX,OAAA;UAAMM,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKM,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BP,OAAA;QAAIM,SAAS,EAAC,UAAU;QAAAC,QAAA,EACrBL,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAClBb,OAAA;UAAoBM,SAAS,EAAC,UAAU;UAAAC,QAAA,eACtCP,OAAA,CAACF,OAAO;YACNgB,EAAE,EAAED,IAAI,CAACV,IAAK;YACdG,SAAS,EAAEA,CAAC;cAAES;YAAS,CAAC,KACtB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;YAAAR,QAAA,gBAEDP,OAAA;cAAMM,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEM,IAAI,CAACR;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CX,OAAA;cAAMM,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEM,IAAI,CAACT;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC,GATHE,IAAI,CAACV,IAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUd,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENX,OAAA;MAAKM,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BP,OAAA;QAAKM,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BP,OAAA;UAAAO,QAAA,EAAM;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GAhDIf,OAAiB;AAkDvB,eAAeA,OAAO;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}