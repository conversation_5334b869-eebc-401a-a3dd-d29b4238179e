[{"D:\\LG\\src\\index.tsx": "1", "D:\\LG\\src\\App.tsx": "2", "D:\\LG\\src\\components\\ProtectedRoute.tsx": "3", "D:\\LG\\src\\components\\Layout\\Layout.tsx": "4", "D:\\LG\\src\\contexts\\AuthContext.tsx": "5", "D:\\LG\\src\\components\\Dashboard\\Dashboard.tsx": "6", "D:\\LG\\src\\components\\Login\\Login.tsx": "7", "D:\\LG\\src\\components\\AllTransactions\\AllTransactions.tsx": "8", "D:\\LG\\src\\components\\Layout\\Sidebar.tsx": "9", "D:\\LG\\src\\components\\Layout\\Header.tsx": "10", "D:\\LG\\src\\api.tsx": "11"}, {"size": 252, "mtime": 1754374536632, "results": "12", "hashOfConfig": "13"}, {"size": 1625, "mtime": 1754374512394, "results": "14", "hashOfConfig": "13"}, {"size": 729, "mtime": 1754374240915, "results": "15", "hashOfConfig": "13"}, {"size": 477, "mtime": 1754374353168, "results": "16", "hashOfConfig": "13"}, {"size": 1703, "mtime": 1754374234051, "results": "17", "hashOfConfig": "13"}, {"size": 4576, "mtime": 1754375465552, "results": "18", "hashOfConfig": "13"}, {"size": 4218, "mtime": 1754374263934, "results": "19", "hashOfConfig": "13"}, {"size": 7275, "mtime": 1754374463373, "results": "20", "hashOfConfig": "13"}, {"size": 1328, "mtime": 1754374299943, "results": "21", "hashOfConfig": "13"}, {"size": 3909, "mtime": 1754374569462, "results": "22", "hashOfConfig": "13"}, {"size": 2908, "mtime": 1754374218338, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4oneol", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\LG\\src\\index.tsx", [], [], "D:\\LG\\src\\App.tsx", [], [], "D:\\LG\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\LG\\src\\components\\Layout\\Layout.tsx", [], [], "D:\\LG\\src\\contexts\\AuthContext.tsx", [], [], "D:\\LG\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\LG\\src\\components\\Login\\Login.tsx", [], [], "D:\\LG\\src\\components\\AllTransactions\\AllTransactions.tsx", [], [], "D:\\LG\\src\\components\\Layout\\Sidebar.tsx", [], [], "D:\\LG\\src\\components\\Layout\\Header.tsx", [], [], "D:\\LG\\src\\api.tsx", [], []]