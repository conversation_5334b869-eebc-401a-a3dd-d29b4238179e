.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.dashboard-header p {
  color: #718096;
  margin: 0;
  font-size: 1rem;
}

.dashboard-stats {
  margin-bottom: 2rem;
}

/* Force 4 columns on large screens */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--platform-color, #667eea);
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-icon {
  font-size: 2rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
  border-radius: 0.75rem;
}

.stat-menu-button {
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s, background-color 0.2s;
}

.stat-menu-button:hover {
  color: #4a5568;
  background: #f7fafc;
}

.stat-content {
  margin-bottom: 1rem;
}

.stat-platform {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--platform-color, #667eea);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #718096;
  font-size: 0.875rem;
}

.stat-footer {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.trend-indicator {
  font-size: 0.875rem;
  font-weight: 600;
}

.trend-indicator.positive {
  color: #38a169;
}

.trend-indicator.negative {
  color: #e53e3e;
}

.trend-text {
  font-size: 0.875rem;
  color: #718096;
}

.dashboard-summary {
  margin-top: 2rem;
}

.summary-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.summary-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-label {
  font-size: 0.875rem;
  color: #718096;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  color: #718096;
  font-size: 1rem;
}

.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #feb2b2;
}

/* Responsive overrides for cards per row */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  .summary-stats {
    grid-template-columns: 1fr;
  }
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
}
