import React, { useState, useEffect } from 'react';
import { apiService } from '../../api';
import './Dashboard.css';

interface DashboardStat {
  platform: string;
  count: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        const data = await apiService.getDashboardStats();
        setStats(data);
      } catch (error) {
        setError('Failed to load dashboard data');
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getPlatformIcon = (platform: string): string => {
    const icons: { [key: string]: string } = {
      'Facebook': '📘',
      'Google': '🔍',
      'Twitter': '🐦',
      'LinkedIn': '💼',
      'Instagram': '📷',
      'YouTube': '📺'
    };
    return icons[platform] || '🔗';
  };

  const getPlatformColor = (platform: string): string => {
    const colors: { [key: string]: string } = {
      'Facebook': '#1877f2',
      'Google': '#4285f4',
      'Twitter': '#1da1f2',
      'LinkedIn': '#0077b5',
      'Instagram': '#e4405f',
      'YouTube': '#ff0000'
    };
    return colors[platform] || '#667eea';
  };

  if (isLoading) {
    return (
      <div className="dashboard">
        <div className="dashboard-header">
          <h1>Dashboard</h1>
          <p>Welcome back! Here's what's happening with your integrations.</p>
        </div>
        <div className="loading-container">
          <div className="loading-spinner">Loading dashboard...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard">
        <div className="dashboard-header">
          <h1>Dashboard</h1>
          <p>Welcome back! Here's what's happening with your integrations.</p>
        </div>
        <div className="error-container">
          <div className="error-message">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <p>Welcome back! Here's what's happening with your integrations.</p>
      </div>

      <div className="dashboard-stats">
        <div className="stats-grid">
          {stats.map((stat) => (
            <div 
              key={stat.platform} 
              className="stat-card"
              style={{ '--platform-color': getPlatformColor(stat.platform) } as React.CSSProperties}
            >
              <div className="stat-card-header">
                <div className="stat-icon">
                  {getPlatformIcon(stat.platform)}
                </div>
                <div className="stat-menu">
                  <button className="stat-menu-button">⋯</button>
                </div>
              </div>
              
              <div className="stat-content">
                <h3 className="stat-platform">{stat.platform}</h3>
                <div className="stat-number">{stat.count}</div>
                <div className="stat-label">Active Connections</div>
              </div>

              <div className="stat-footer">
                <div className="stat-trend">
                  <span className="trend-indicator positive">↗</span>
                  <span className="trend-text">+12% from last month</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="dashboard-summary">
        <div className="summary-card">
          <h3>Quick Summary</h3>
          <div className="summary-stats">
            <div className="summary-item">
              <span className="summary-label">Total Integrations</span>
              <span className="summary-value">{stats.reduce((sum, stat) => sum + stat.count, 0)}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Active Platforms</span>
              <span className="summary-value">{stats.length}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">This Month</span>
              <span className="summary-value">+{Math.floor(Math.random() * 20) + 5}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
