{"ast": null, "code": "import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\";\n// default level is set on the client side, so it does not need\n// to be set by the CLI or API\nvar defaultLevel = \"info\";\n\n// options new options, merge with old options\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n  var logString = \"Server started:\";\n\n  // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  }\n  // replace last comma with a period\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\nexport { log, logEnabledFeatures, setLogLevel };", "map": {"version": 3, "names": ["logger", "name", "defaultLevel", "setLogLevel", "level", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "<PERSON><PERSON><PERSON><PERSON>", "logEnabledFeatures", "features", "enabledFeatures", "Object", "keys", "length", "logString", "i", "key", "concat", "slice", "info"], "sources": ["D:/LG/node_modules/webpack-dev-server/client/utils/log.js"], "sourcesContent": ["import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\";\n// default level is set on the client side, so it does not need\n// to be set by the CLI or API\nvar defaultLevel = \"info\";\n\n// options new options, merge with old options\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n  var logString = \"Server started:\";\n\n  // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  }\n  // replace last comma with a period\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\nexport { log, logEnabledFeatures, setLogLevel };"], "mappings": "AAAA,OAAOA,MAAM,MAAM,4BAA4B;AAC/C,IAAIC,IAAI,GAAG,oBAAoB;AAC/B;AACA;AACA,IAAIC,YAAY,GAAG,MAAM;;AAEzB;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1BJ,MAAM,CAACK,sBAAsB,CAAC;IAC5BD,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACAD,WAAW,CAACD,YAAY,CAAC;AACzB,IAAII,GAAG,GAAGN,MAAM,CAACO,SAAS,CAACN,IAAI,CAAC;AAChC,IAAIO,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;EAC7D,IAAIC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC;EAC3C,IAAI,CAACA,QAAQ,IAAIC,eAAe,CAACG,MAAM,KAAK,CAAC,EAAE;IAC7C;EACF;EACA,IAAIC,SAAS,GAAG,iBAAiB;;EAEjC;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,eAAe,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/C,IAAIC,GAAG,GAAGN,eAAe,CAACK,CAAC,CAAC;IAC5BD,SAAS,IAAI,GAAG,CAACG,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,CAACR,QAAQ,CAACO,GAAG,CAAC,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,CAAC;EACvF;EACA;EACAF,SAAS,GAAGA,SAAS,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACD,MAAM,CAAC,GAAG,CAAC;EAC9CX,GAAG,CAACa,IAAI,CAACL,SAAS,CAAC;AACrB,CAAC;AACD,SAASR,GAAG,EAAEE,kBAAkB,EAAEL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}