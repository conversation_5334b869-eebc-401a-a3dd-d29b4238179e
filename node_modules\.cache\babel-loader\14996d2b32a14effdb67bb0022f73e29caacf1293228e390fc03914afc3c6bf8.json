{"ast": null, "code": "var _jsxFileName = \"D:\\\\LG\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  const location = useLocation();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"J7RTr1FGmaNUDR5R5RXW3Wn2R7A=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "_s", "isAuthenticated", "isLoading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["D:/LG/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {\n  const { isAuthenticated, isLoading } = useAuth();\n  const location = useLocation();\n\n  if (isLoading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading-spinner\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMlD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGT,OAAO,CAAC,CAAC;EAChD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,IAAIU,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKS,SAAS,EAAC,mBAAmB;MAAAL,QAAA,eAChCJ,OAAA;QAAKS,SAAS,EAAC,iBAAiB;QAAAL,QAAA,EAAC;MAAU;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAEV;EAEA,IAAI,CAACP,eAAe,EAAE;IACpB;IACA,oBAAON,OAAA,CAACJ,QAAQ;MAACkB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,oBAAOb,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAlBIF,cAA6C;EAAA,QACVL,OAAO,EAC7BD,WAAW;AAAA;AAAAqB,EAAA,GAFxBf,cAA6C;AAoBnD,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}